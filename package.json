{"name": "react18_ts_music", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@reduxjs/toolkit": "^1.8.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.0", "@types/react": "^18.0.22", "@types/react-dom": "^18.0.7", "antd": "^4.23.6", "axios": "^1.1.3", "classnames": "^2.3.2", "craco-less": "^2.1.0-alpha.0", "normalize.css": "^8.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.4", "react-router-dom": "^6.4.2", "react-scripts": "5.0.1", "typescript": "^4.8.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "prettier": "prettier --write ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.0.0-alpha.8", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.10", "prettier": "^2.7.1", "styled-components": "^5.3.6"}}